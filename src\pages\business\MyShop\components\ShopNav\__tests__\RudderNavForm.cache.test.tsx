import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react';
import { Form } from 'antd';
import RudderNavForm from '../RudderNavForm';

// Mock ProFormInstance
const mockFormRef = {
  current: {
    getFieldValue: jest.fn(),
    setFieldsValue: jest.fn(),
  },
};

const mockListActionRef = { current: undefined };

describe('RudderNavForm 缓存功能测试', () => {
  const defaultProps = {
    formRef: mockFormRef as any,
    listActionRef: mockListActionRef as any,
    tabList: [],
    setTabList: jest.fn(),
    setIsModalOpen: jest.fn(),
    currentCount: 5,
    onCountChange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('应该在切换导航数量时保存和恢复缓存数据', () => {
    // 模拟5个导航的初始数据，位置1和位置3有用户输入
    const initialNavData = [
      { name: '首页', icon: '1_1', icon_active: '1_1_' },
      { name: '自定义导航2', icon: 'custom2', icon_active: 'custom2_active', link: 'custom-link' },
      { name: '商品工坊', icon: '', icon_active: 'default_center_icon' },
      { name: '自定义导航4', icon: 'custom4', icon_active: 'custom4_active', link: 'another-link' },
      { name: '我的', icon: '1_4', icon_active: '1_4_' },
    ];

    mockFormRef.current.getFieldValue.mockReturnValue(initialNavData);

    const { rerender } = render(<RudderNavForm {...defaultProps} />);

    // 模拟切换到3个导航
    const radio3 = screen.getByLabelText('3个');
    fireEvent.click(radio3);

    // 验证 setFieldsValue 被调用，且只包含3个导航
    expect(mockFormRef.current.setFieldsValue).toHaveBeenCalled();
    const setFieldsValueCall = mockFormRef.current.setFieldsValue.mock.calls[0][0];
    expect(setFieldsValueCall.shopNav).toHaveLength(3);

    // 模拟切换回5个导航时的表单数据（3个导航的状态）
    const threeNavData = [
      { name: '首页', icon: '1_1', icon_active: '1_1_' },
      { name: '商品工坊', icon: '', icon_active: 'default_center_icon' },
      { name: '我的', icon: '1_4', icon_active: '1_4_' },
    ];
    mockFormRef.current.getFieldValue.mockReturnValue(threeNavData);

    // 切换回5个导航
    const radio5 = screen.getByLabelText('5个');
    fireEvent.click(radio5);

    // 验证缓存的数据被恢复
    expect(mockFormRef.current.setFieldsValue).toHaveBeenCalledTimes(2);
    const secondCall = mockFormRef.current.setFieldsValue.mock.calls[1][0];
    expect(secondCall.shopNav).toHaveLength(5);
    
    // 验证位置1和位置3的数据被恢复
    expect(secondCall.shopNav[1]).toEqual({
      name: '自定义导航2',
      icon: 'custom2',
      icon_active: 'custom2_active',
      link: 'custom-link',
    });
    expect(secondCall.shopNav[3]).toEqual({
      name: '自定义导航4',
      icon: 'custom4',
      icon_active: 'custom4_active',
      link: 'another-link',
    });
  });

  test('应该只缓存有内容的导航项', () => {
    // 模拟5个导航，其中位置1有内容，位置3为空
    const navDataWithEmptyPosition = [
      { name: '首页', icon: '1_1', icon_active: '1_1_' },
      { name: '有内容的导航', icon: 'icon1', icon_active: 'icon1_active' },
      { name: '商品工坊', icon: '', icon_active: 'default_center_icon' },
      { name: '', icon: '', icon_active: '' }, // 空的导航项
      { name: '我的', icon: '1_4', icon_active: '1_4_' },
    ];

    mockFormRef.current.getFieldValue.mockReturnValue(navDataWithEmptyPosition);

    render(<RudderNavForm {...defaultProps} />);

    // 切换到3个导航
    const radio3 = screen.getByLabelText('3个');
    fireEvent.click(radio3);

    // 模拟3个导航状态
    mockFormRef.current.getFieldValue.mockReturnValue([
      { name: '首页', icon: '1_1', icon_active: '1_1_' },
      { name: '商品工坊', icon: '', icon_active: 'default_center_icon' },
      { name: '我的', icon: '1_4', icon_active: '1_4_' },
    ]);

    // 切换回5个导航
    const radio5 = screen.getByLabelText('5个');
    fireEvent.click(radio5);

    const finalCall = mockFormRef.current.setFieldsValue.mock.calls[1][0];
    
    // 验证位置1的有内容数据被恢复
    expect(finalCall.shopNav[1]).toEqual({
      name: '有内容的导航',
      icon: 'icon1',
      icon_active: 'icon1_active',
    });
    
    // 验证位置3的空数据不被恢复（应该是默认的空对象）
    expect(finalCall.shopNav[3]).toEqual({
      name: '',
      icon: '',
      icon_active: '',
    });
  });
});

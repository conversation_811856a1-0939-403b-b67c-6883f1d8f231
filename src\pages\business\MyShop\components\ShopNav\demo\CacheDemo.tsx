import React, { useRef, useState } from 'react';
import { <PERSON><PERSON>, Card, Space, Typography, Divider } from 'antd';
import type { ProFormInstance, FormListActionType } from '@ant-design/pro-components';
import RudderNavForm from '../RudderNavForm';

const { Title, Paragraph, Text } = Typography;

/**
 * RudderNavForm 缓存功能演示组件
 * 
 * 这个演示展示了导航数量切换时的缓存功能：
 * 1. 在5个导航模式下输入内容
 * 2. 切换到3个导航
 * 3. 切换回5个导航时，之前的内容会被恢复
 */
const CacheDemo: React.FC = () => {
  const formRef = useRef<ProFormInstance>();
  const listActionRef = useRef<FormListActionType>();
  const [tabList, setTabList] = useState([
    { name: '首页', icon: '1_1', icon_active: '1_1_' },
    { name: '', icon: '', icon_active: '' },
    { name: '商品工坊', icon: '', icon_active: 'default_center_icon' },
    { name: '', icon: '', icon_active: '' },
    { name: '我的', icon: '1_4', icon_active: '1_4_' },
  ]);
  const [currentCount, setCurrentCount] = useState(5);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  const handleCountChange = (count: number, nextList: any[]) => {
    setCurrentCount(count);
    addLog(`导航数量切换到: ${count}个`);
    addLog(`当前导航数据: ${JSON.stringify(nextList.map(item => ({ 
      name: item.name || '(空)', 
      hasIcon: !!item.icon,
      hasActiveIcon: !!item.icon_active,
      hasLink: !!item.link 
    })))}`);
  };

  const handleSetModalOpen = (index: number) => {
    addLog(`打开图标选择弹窗: 位置${index}`);
  };

  const simulateUserInput = () => {
    // 模拟用户在位置1和位置3输入内容
    const mockData = [
      { name: '首页', icon: '1_1', icon_active: '1_1_' },
      { name: '自定义导航2', icon: 'custom2', icon_active: 'custom2_active', link: 'custom-link' },
      { name: '商品工坊', icon: '', icon_active: 'default_center_icon' },
      { name: '自定义导航4', icon: 'custom4', icon_active: 'custom4_active', link: 'another-link' },
      { name: '我的', icon: '1_4', icon_active: '1_4_' },
    ];
    
    formRef.current?.setFieldsValue({ shopNav: mockData });
    setTabList(mockData);
    addLog('模拟用户输入: 在位置1和位置3添加了自定义内容');
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const getCurrentFormData = () => {
    const data = formRef.current?.getFieldValue('shopNav') || [];
    addLog(`当前表单数据: ${JSON.stringify(data.map((item: any, index: number) => ({
      position: index,
      name: item.name || '(空)',
      hasContent: !!(item.name || item.icon || item.icon_active || item.link)
    })))}`);
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Title level={2}>RudderNavForm 缓存功能演示</Title>
      
      <Paragraph>
        这个演示展示了导航数量切换时的缓存功能。当用户在5个导航模式下输入内容后切换到3个导航，
        再切换回5个导航时，之前输入的内容会被自动恢复。
      </Paragraph>

      <Space style={{ marginBottom: '16px' }}>
        <Button type="primary" onClick={simulateUserInput}>
          模拟用户输入
        </Button>
        <Button onClick={getCurrentFormData}>
          查看当前数据
        </Button>
        <Button onClick={clearLogs}>
          清空日志
        </Button>
      </Space>

      <div style={{ display: 'flex', gap: '24px' }}>
        <div style={{ flex: 1 }}>
          <Card title="导航表单" size="small">
            <RudderNavForm
              formRef={formRef}
              listActionRef={listActionRef}
              tabList={tabList}
              setTabList={setTabList}
              setIsModalOpen={handleSetModalOpen}
              currentCount={currentCount}
              onCountChange={handleCountChange}
            />
          </Card>
        </div>

        <div style={{ width: '400px' }}>
          <Card title="操作日志" size="small">
            <div style={{ height: '400px', overflow: 'auto' }}>
              {logs.length === 0 ? (
                <Text type="secondary">暂无日志</Text>
              ) : (
                logs.map((log, index) => (
                  <div key={index} style={{ marginBottom: '8px', fontSize: '12px' }}>
                    <Text code>{log}</Text>
                  </div>
                ))
              )}
            </div>
          </Card>
        </div>
      </div>

      <Divider />

      <Card title="测试步骤" size="small">
        <ol>
          <li>点击"模拟用户输入"按钮，在位置1和位置3添加自定义内容</li>
          <li>将导航数量从"5个"切换到"3个"</li>
          <li>再将导航数量从"3个"切换回"5个"</li>
          <li>观察位置1和位置3的内容是否被恢复</li>
          <li>查看右侧日志了解详细的数据变化过程</li>
        </ol>
      </Card>

      <Card title="预期结果" size="small" style={{ marginTop: '16px' }}>
        <ul>
          <li><Text strong>步骤2后：</Text>只显示3个导航（首页、商品工坊、我的），位置1和位置3的内容被隐藏但保存在缓存中</li>
          <li><Text strong>步骤3后：</Text>显示5个导航，位置1和位置3的自定义内容被自动恢复</li>
          <li><Text strong>数据提交：</Text>在3个导航模式下提交时，只会提交3个导航的数据，缓存的数据不会被提交</li>
        </ul>
      </Card>
    </div>
  );
};

export default CacheDemo;
